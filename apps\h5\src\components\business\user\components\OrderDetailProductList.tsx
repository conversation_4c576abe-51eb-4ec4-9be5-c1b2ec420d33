import React from 'react'
import { useTranslations } from 'next-intl'
import {
  Membership,
  mergeStyles,
  OrderDetail,
  OrderMigratedDetail,
  OrderVouchers,
} from '@ninebot/core'

import { Arrow } from '@/components/icons'
import { commonStyles } from '@/constants'
import { useRouter } from '@/i18n/navigation'

import { MigrationProductItem } from './migrationProductItem'
import ProductItem from './ProductItem'
import ProductListSkeleton from './ProductListSkeleton'

type OrderDetailProductListProps = {
  isSkinSound?: boolean
  isMigrated?: boolean
  migrationData?: OrderMigratedDetail
  orderData: OrderDetail | null | undefined
  handleOpenInstruction: (html: string) => void
  setCouponList: (list: OrderVouchers) => void
  setCouponVisible: (visible: boolean) => void
  setMembershipList: (list: Membership[]) => void
  setMembershipVisible: (visible: boolean) => void
}

const styles = {
  couponStatus: 'py-[4px] px-[12px] bg-[#FEE5E5] rounded-[4px] ',
  couponStatusText: 'text-[14px] leading-8 text-[#DA291C] font-miSansMedium380',
  couponDisabled: 'bg-[#F3F3F4] ',
  couponTextDisabled: 'text-[#6E6E73] ',
  couponDate: 'text-[12px] leading-[16px] text-[#6E6E73] font-miSansRegular330',
}

/**
 * 订单详情商品列表
 */
const OrderDetailProductList = (props: OrderDetailProductListProps) => {
  const {
    isSkinSound = false,
    isMigrated = false,
    orderData,
    migrationData,
    handleOpenInstruction,
    setCouponList,
    setCouponVisible,
    setMembershipList,
    setMembershipVisible,
  } = props

  const getI18nString = useTranslations('Common')
  const router = useRouter()

  // 判断是否显示骨架屏
  const shouldShowSkeleton = isMigrated ? !migrationData : !orderData

  // 如果需要显示骨架屏，直接返回骨架屏组件
  if (shouldShowSkeleton) {
    return <ProductListSkeleton rows={3} />
  }

  return (
    <div className="mb-[8px] rounded-[12px] bg-white px-[12px] py-base-16">
      <div className={mergeStyles([commonStyles.font_16_bold, 'mb-[24px]'])}>
        {getI18nString(isSkinSound ? 'skin_sound' : 'product_list')}
      </div>
      {isMigrated && migrationData
        ? migrationData?.items?.map((item, index) => {
            if (item) {
              return (
                <div key={`${item?.product_title}-${index}`} className="mt-8">
                  <MigrationProductItem productInfo={item} />
                </div>
              )
            }
          })
        : Number(orderData?.items?.length) > 0 &&
          orderData?.items?.map((item) => {
            return (
              <div key={item?.id} className="mt-8">
                <ProductItem
                  productInfo={item}
                  showQty={true}
                  isShowInstruction={!!item?.product?.description?.html}
                  setInstructionVisible={() =>
                    handleOpenInstruction(item?.product?.description?.html || '')
                  }
                />
                {/* 代金券 */}
                {item?.coupons && Number(item?.coupons?.item_count) > 0 && (
                  <div>
                    <div className={mergeStyles([commonStyles.flex_row, 'mt-[24px]'])}>
                      <div className="flex flex-col gap-[4px]">
                        <span className={commonStyles.font_14_light}>
                          {getI18nString('coupons_count', { key: item?.coupons?.item_count })}
                        </span>
                        <span className={styles.couponDate}>
                          {getI18nString('expired_time_until')}{' '}
                          {item?.coupons?.expired_at?.split(' ')[0]}
                        </span>
                      </div>

                      <button
                        className="flex items-center gap-[4px]"
                        onClick={() => router.push('/customer/coupons')}>
                        {item?.coupons?.status === '0' ? (
                          <div className={styles.couponStatus}>
                            <span className={styles.couponStatusText}>
                              {getI18nString('coupons_wait_use', {
                                key: item?.coupons?.waiting_use_count,
                              })}
                            </span>
                          </div>
                        ) : (
                          <div
                            className={mergeStyles([styles.couponStatus, styles.couponDisabled])}>
                            <span
                              className={mergeStyles([
                                styles.couponStatusText,
                                styles.couponTextDisabled,
                              ])}>
                              {item?.coupons?.status_label}
                            </span>
                          </div>
                        )}

                        <Arrow color="#86868B" />
                      </button>
                    </div>
                  </div>
                )}
                {/* 服务包 */}
                {item?.vouchers && Number(item?.vouchers?.items?.length) > 0 && (
                  <div>
                    <div className={mergeStyles(commonStyles.flex_row, 'mt-[24px]')}>
                      <div className="flex flex-col gap-[4px]">
                        <span className={commonStyles.font_14_light}>
                          {getI18nString('vouchers_count', { key: item?.vouchers?.item_count })}
                        </span>
                        <span className={styles.couponDate}>
                          {getI18nString('expired_time_until')}{' '}
                          {item?.vouchers?.expired_at?.split(' ')[0]}
                        </span>
                      </div>

                      <button
                        className="flex items-center gap-[4px]"
                        onClick={() => {
                          if (item?.vouchers?.items) {
                            setCouponList(item?.vouchers?.items)
                            setCouponVisible(true)
                          }
                        }}>
                        {item?.vouchers?.status === '0' ? (
                          <div className={styles.couponStatus}>
                            <span className={styles.couponStatusText}>
                              {getI18nString('coupons_wait_use', {
                                key: item?.vouchers?.waiting_use_count,
                              })}
                            </span>
                          </div>
                        ) : (
                          <div
                            className={mergeStyles([styles.couponStatus, styles.couponDisabled])}>
                            <span
                              className={mergeStyles([
                                styles.couponStatusText,
                                styles.couponTextDisabled,
                              ])}>
                              {item?.vouchers?.status_label}
                            </span>
                          </div>
                        )}

                        <Arrow color="#86868B" />
                      </button>
                    </div>
                  </div>
                )}
                {/* 数字会员 */}
                {item?.third_platform_member_code &&
                  Number(item?.third_platform_member_code?.items?.length) > 0 && (
                    <div className={mergeStyles([commonStyles.flex_row, 'mt-[24px]'])}>
                      <div className="flex flex-col gap-[4px]">
                        <span className={commonStyles.font_14_light}>
                          {getI18nString('vouchers_count', {
                            key: item?.third_platform_member_code?.item_count,
                          })}
                        </span>
                        <span className={styles.couponDate}>
                          {getI18nString('expired_time_until')}{' '}
                          {item?.third_platform_member_code?.expired_at?.split(' ')[0]}
                        </span>
                      </div>
                      <button
                        onClick={() => {
                          if (item?.third_platform_member_code?.items) {
                            setMembershipList(item?.third_platform_member_code?.items)
                            setMembershipVisible(true)
                          }
                        }}>
                        <div className="flex items-center justify-between gap-[4px]">
                          {item?.third_platform_member_code?.status === '0' ? (
                            <div className={styles.couponStatus}>
                              <span className={styles.couponStatusText}>
                                {getI18nString('coupons_wait_use', {
                                  key: item?.third_platform_member_code?.waiting_use_count,
                                })}
                              </span>
                            </div>
                          ) : (
                            <div
                              className={mergeStyles([styles.couponStatus, styles.couponDisabled])}>
                              <span
                                className={mergeStyles([
                                  styles.couponStatusText,
                                  styles.couponTextDisabled,
                                ])}>
                                {item?.third_platform_member_code?.status_label}
                              </span>
                            </div>
                          )}

                          <Arrow color="#86868B" />
                        </div>
                      </button>
                    </div>
                  )}
                {/* 皮肤音效 */}
                {item?.digital_info?.sn_code && (
                  <div>
                    <div className={mergeStyles([commonStyles.flex_row, 'mt-[24px]'])}>
                      <div className="flex flex-col gap-[4px]">
                        <span className={styles.couponDate}>
                          {getI18nString('valid_time', {
                            key: item?.use_from_date,
                            key2: item?.use_to_date,
                          })}
                        </span>
                      </div>

                      <button className="flex items-center gap-[4px]">
                        {item?.requisition_status_label || item?.digital_info?.status === 0 ? (
                          <div className={styles.couponStatus}>
                            <span className={styles.couponStatusText}>
                              {item?.requisition_status_label || item?.digital_info?.label}
                            </span>
                          </div>
                        ) : (
                          <div
                            className={mergeStyles([styles.couponStatus, styles.couponDisabled])}>
                            <span
                              className={mergeStyles([
                                styles.couponStatusText,
                                styles.couponTextDisabled,
                              ])}>
                              {item?.digital_info?.label}
                            </span>
                          </div>
                        )}
                      </button>
                    </div>
                  </div>
                )}
              </div>
            )
          })}
    </div>
  )
}

export default OrderDetailProductList
