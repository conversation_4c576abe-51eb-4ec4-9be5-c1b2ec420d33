'use client'

import React, { useEffect, useMemo, useState } from 'react'
import { useTranslations } from 'next-intl'
import upload from '@public/icons/upload.png'
import {
  generateOSSUrl,
  mergeStyles,
  OrderReturnFormatItem,
  resolveCatchMessage,
  ROUTE,
  selectUserAddressById,
  sleep,
  useApplyOrderReturnMutation,
  useDebounceFn,
  useLoadingContext,
  userAddressDefaultSelector,
  userOrderReturnAddressIdSelector,
  userOrderSelectedProductsSelector,
  useToastContext,
  useUserAddress,
} from '@ninebot/core'
import { useNavigate } from '@ninebot/core/src/businessHooks'
import { OrderRequisitionInput } from '@ninebot/core/src/graphql/generated/graphql'
import { useAppSelector } from '@ninebot/core/src/store/hooks'
import { But<PERSON>, Image, ImageUploader, TextArea } from 'antd-mobile'
import { ImageUploadItem } from 'antd-mobile/es/components/image-uploader'

import { CustomButton, CustomCheckbox, CustomNavBar } from '@/components'
import { commonStyles } from '@/constants'
import { useRouter } from '@/i18n/navigation'

const styles = {
  check: 'w-8 h-8 rounded-[10px] border-[1.4px] border-[#BBBBBD]',
  checkActive: 'flex flex-row justify-center items-center bg-[#DA291C] border-0',
  item: 'mr-[8px] mb-8 rounded-[8px]',
  lastItem: 'mr-0',
  uploadText:
    'mt-[8px] text-[14px] leading-[17px] text-[#000000] font-miSansRegular330 whitespace-nowrap',
}

const MAX_UPLOAD_COUNT = 9 // 最大上传数量

type ImageParams = {
  name?: string
  base64_encoded_file?: string
}

type ImageItem = ImageUploadItem & ImageParams

/**
 * 申请退款/退货
 */
const AccountOrderReturn = ({
  orderNumber,
  isShowAddress,
}: {
  orderNumber: string
  isShowAddress: string
}) => {
  const getI18nString = useTranslations('Common')
  const toast = useToastContext()
  const loading = useLoadingContext()
  const { openPage } = useNavigate()
  const router = useRouter()

  const REASONS = [
    getI18nString('seven_days_without_reason'),
    getI18nString('brought_wrong'),
    getI18nString('not_want_anymore'),
    getI18nString('quality_problem'),
    getI18nString('manual_service'),
    getI18nString('other'),
  ]

  const { fetchUserAddresses } = useUserAddress()
  const addressId = useAppSelector(userOrderReturnAddressIdSelector)
  const selectedProducts = useAppSelector(userOrderSelectedProductsSelector)
  const address = useAppSelector((state) => selectUserAddressById(state, String(addressId)))
  const userAddressDefault = useAppSelector(userAddressDefaultSelector)

  const [reason, setReason] = useState('')
  const [description, setDescription] = useState('')

  const [uploadFile, setUploadFile] = useState<ImageItem[]>([])
  const [uploadFormData, setUploadFormData] = useState<ImageParams[]>([])

  const showAddress = useMemo(() => {
    return isShowAddress === 'true'
  }, [isShowAddress])

  const [applyOrderReturn] = useApplyOrderReturnMutation()

  /**
   * 寄件地址
   */
  const shippingAddress = useMemo(() => {
    return address || userAddressDefault
  }, [address, userAddressDefault])

  /**
   * 寄件地址 ID
   */
  const shippingAddressId = useMemo(() => {
    return shippingAddress?.address_id
  }, [shippingAddress])

  /**
   * 提交退货
   */
  const { run: handleCancelOrder } = useDebounceFn(() => {
    if (!orderNumber) {
      toast.show({
        content: getI18nString('fetch_data_error'),
        icon: 'fail',
      })
      return
    }
    if (!reason) {
      toast.show({
        content: getI18nString('select_at_least_one_reason'),
      })
      return
    }

    const params: {
      orderNumber: string
      reason: string
      address_id: string | number
      items: OrderReturnFormatItem[]
      comment: string
      images: ImageParams[]
    } = {
      orderNumber: orderNumber,
      reason: reason,
      address_id: '0',
      items: [],
      comment: '',
      images: [],
    }

    if (showAddress && shippingAddressId) {
      params.address_id = shippingAddressId
    }
    if (selectedProducts?.length > 0) {
      params.items = selectedProducts
    }
    if (description) {
      params.comment = description
    }
    if (uploadFormData.length > 0) {
      params.images = uploadFormData
    }

    loading.show()
    applyOrderReturn({
      input: params as OrderRequisitionInput,
    })
      .unwrap()
      .then(async (res) => {
        loading.hide()
        await sleep(500)
        if (res?.orderRequisition?.status) {
          toast.show({
            content: getI18nString('application_submitted'),
            icon: 'success',
          })
          await sleep(500)
          router.back()
        } else {
          toast.show({
            icon: 'fail',
            content: res?.orderRequisition?.message || getI18nString('fetch_data_error'),
          })
        }
      })
      .catch(async (error) => {
        loading.hide()
        await sleep(500)
        toast.show({
          icon: 'fail',
          content: resolveCatchMessage(error) as string,
        })
      })
  })

  const handleUpload = async (file: File): Promise<ImageItem> => {
    // 使用FileReader将blob转换为base64
    const reader = new FileReader()

    return new Promise((resolve, reject) => {
      reader.onloadend = () => {
        // 返回上传后的结果，包括base64和文件名
        if (typeof reader.result === 'string') {
          resolve({
            base64_encoded_file: `base64,${reader?.result?.split?.(',')[1]}`,
            name: file.name,
            url: reader?.result,
          })
        }
      }
      reader.onerror = (error) => reject(error)
      reader.readAsDataURL(file)
    })
  }

  /**
   * 修改地址
   */
  const { run: handleChangeAddress } = useDebounceFn(() => {
    openPage({
      route: ROUTE.accountAddress,
      isBack: true,
      from: ROUTE.accountOrderReturn,
    })
  })

  /**
   * 进入退款页获取最新地址信息
   */
  useEffect(() => {
    fetchUserAddresses()
  }, [fetchUserAddresses])

  /**
   * 设置上传图片数据
   */
  useEffect(() => {
    setUploadFormData(
      uploadFile.map((item) => ({
        name: item.name,
        base64_encoded_file: item.base64_encoded_file,
      })),
    )
  }, [uploadFile])

  /**
   * 未获取到订单数据时
   */
  useEffect(() => {
    if (!orderNumber && !addressId) {
      router.back()
    }
  }, [addressId, orderNumber, router])

  return (
    <div className="bg-[#F8F8F9]">
      <CustomNavBar title={getI18nString('return_reason')} />
      <div className="mx-[12px]">
        <div className="my-[8px] rounded-[12px] bg-white px-8 py-8">
          <div className="mb-8 flex flex-row items-center justify-start">
            <div className="text-lg leading-[22px]">{getI18nString('return_reason')}</div>
            <div className={mergeStyles([commonStyles.font_red, 'ml-[8px] mt-[6px]'])}>*</div>
          </div>
          {REASONS.map((option) => (
            <div key={option} className="mt-8 flex flex-row items-center gap-[8px]">
              <CustomCheckbox
                checked={reason === option}
                onChange={(checked) => {
                  if (checked) {
                    setReason(option)
                  }
                }}
              />
              <div className="font-miSansRegular330 text-[14px] leading-8 text-[#0F0F0F]">
                {option}
              </div>
            </div>
          ))}
        </div>

        {showAddress && addressId ? (
          <div className="my-[8px] rounded-[12px] bg-white p-8">
            <div className="mb-[16px]">
              <div className="text-lg leading-[22px]">{getI18nString('my_sending_address')}</div>
            </div>
            <div className={commonStyles.flex_start}>
              <div className={mergeStyles([commonStyles.font_16_bold, 'font-miSansSemibold520'])}>
                {shippingAddress?.receive_name}
              </div>
              <div
                className={mergeStyles([
                  commonStyles.font_16_bold,
                  ['mx-4', 'font-miSansSemibold520'],
                ])}>
                {shippingAddress?.receive_phone}
              </div>
            </div>
            <div className="mt-[8px] font-miSansRegular330 text-[14px] leading-8 text-[#6E6E73]">
              {shippingAddress?.province} {shippingAddress?.city} {shippingAddress?.county}{' '}
              {shippingAddress?.street}
            </div>
            <CustomButton
              customStyle={{
                height: 18,
                marginTop: 10,
                borderWidth: 1,
                borderColor: '#D1D1D4',
                borderRadius: 4,
                padding: '1px 6px',
              }}
              onClick={handleChangeAddress}>
              <div className="font-miSansRegular330 text-[12px] leading-[16px] text-[#0F0F0F]">
                {getI18nString('update_address')}
              </div>
            </CustomButton>
          </div>
        ) : null}

        <div className="mb-8 rounded-[12px] bg-white px-[16px] py-[16px]">
          <TextArea
            style={{
              marginBottom: 24,
              padding: 0,
              height: 140,
              lineHeight: 26,
              color: '#222223',
              fontFamily: 'var(--font-family-miSansRegular330)',
              fontWeight: 330,
              '--placeholder-color': '#BBBBBD',
              '--font-size': '16px',
            }}
            maxLength={200}
            rows={6}
            value={description}
            onChange={setDescription}
            placeholder={getI18nString('add_description')}
          />

          <ImageUploader
            preview={false}
            className="return-image-uploader"
            value={uploadFile}
            onChange={setUploadFile}
            upload={handleUpload}
            multiple
            columns={uploadFile.length ? 3 : undefined}
            maxCount={MAX_UPLOAD_COUNT}
            showUpload={uploadFile.length < MAX_UPLOAD_COUNT}
            onCountExceed={(exceed) => {
              toast.show({
                icon: 'fail',
                content: getI18nString('max_upload_tip', { key: MAX_UPLOAD_COUNT, key2: exceed }),
              })
            }}
            style={{
              backgroundColor: uploadFile.length ? '#FFFFFF' : '#F8F8F9',
              width: '100%',
              borderRadius: '8px',
            }}
            deleteIcon={
              <Image
                style={{
                  width: 20,
                  height: 20,
                }}
                src={generateOSSUrl('/icons/close2.png')}
                alt="delete"
              />
            }>
            <button
              className="flex flex-col items-center justify-center rounded-base bg-[#F8F8F9]"
              style={
                uploadFile.length > 0
                  ? {
                      width: 100,
                      height: 100,
                    }
                  : {
                      width: '100%',
                      height: 119,
                    }
              }>
              <Image
                style={{
                  width: 26,
                  height: 24,
                }}
                src={generateOSSUrl('/icons/upload.png') || upload.src}
                alt="upload"
              />
              {uploadFile.length > 0 ? (
                <div className={styles.uploadText}>
                  {getI18nString('upload_image_count', {
                    key: MAX_UPLOAD_COUNT - uploadFile.length,
                  })}
                </div>
              ) : (
                <div className={styles.uploadText}>{getI18nString('upload_image')}</div>
              )}
            </button>
          </ImageUploader>
        </div>
      </div>

      <div className="mx-[24px] my-[16px]">
        <Button className="nb-button w-full" color="primary" onClick={handleCancelOrder}>
          <div>{getI18nString('confirm')}</div>
        </Button>
      </div>
    </div>
  )
}

export default AccountOrderReturn
